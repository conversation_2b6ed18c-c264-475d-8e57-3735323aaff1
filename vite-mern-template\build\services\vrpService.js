"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const PurchaseOrder_1 = require("../models/PurchaseOrder");
const User_1 = __importDefault(require("../models/User"));
const mongoose_1 = __importDefault(require("mongoose"));
class VRPService {
    isApplyingSolution = false;
    // Calculate distance between two points using Haversine formula
    calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371; // Earth's radius in kilometers
        const dLat = this.toRadians(lat2 - lat1);
        const dLon = this.toRadians(lon2 - lon1);
        const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
            Math.cos(this.toRadians(lat1)) * Math.cos(this.toRadians(lat2)) *
                Math.sin(dLon / 2) * Math.sin(dLon / 2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return R * c;
    }
    toRadians(degrees) {
        return degrees * (Math.PI / 180);
    }
    // Estimate travel time based on distance (simplified)
    estimateTravelTime(distance) {
        // Assume average speed of 50 km/h in urban areas
        return (distance / 50) * 60; // return time in minutes
    }
    // Get available drivers for VRP
    async getAvailableDrivers() {
        const drivers = await User_1.default.find({
            role: 'trucker',
            status: 'Active',
            isAvailable: { $ne: false }
        });
        return drivers.map(driver => ({
            id: driver._id.toString(),
            userId: driver._id.toString(),
            username: driver.username,
            capacity: driver.vehicleCapacity || 10,
            currentLocation: driver.currentLocation || { latitude: 0, longitude: 0 },
            workingHours: driver.workingHours || { start: '08:00', end: '17:00' },
            maxDeliveries: driver.maxDeliveries || 5,
            isAvailable: driver.isAvailable !== false
        }));
    }
    // Get unassigned delivery locations from purchase orders
    async getUnassignedDeliveries() {
        // Always include all POs that are not completed, regardless of transaction status or delivered quantity
        const unassignedPOs = await PurchaseOrder_1.PurchaseOrder.find({
            isCompleted: false
        });
        const locations = [];
        for (const po of unassignedPOs) {
            // Add pickup location
            locations.push({
                id: `pickup_${po._id}`,
                latitude: po.pickupLocation.coordinates.lat,
                longitude: po.pickupLocation.coordinates.lng,
                address: po.pickupLocation.address,
                type: 'pickup',
                poId: po._id.toString()
            });
            // Add delivery locations for every load, regardless of delivered quantity
            if (po.loads && po.loads.length > 0) {
                po.loads.forEach((load, loadIndex) => {
                    if (load.deliveryLocation && load.deliveryLocation.latitude && load.deliveryLocation.longitude) {
                        const totalQuantity = load.materials.reduce((sum, mat) => sum + mat.quantity, 0);
                        locations.push({
                            id: `delivery_${po._id}_${loadIndex}`,
                            latitude: load.deliveryLocation.latitude,
                            longitude: load.deliveryLocation.longitude,
                            address: `${load.deliveryLocation.street}, ${load.deliveryLocation.city}, ${load.deliveryLocation.state}`,
                            type: 'delivery',
                            poId: po._id.toString(),
                            quantity: totalQuantity,
                            loadType: load.materials[0]?.loadType || 'tons'
                        });
                    }
                });
            }
        }
        return locations;
    }
    // Generate delivery queue with capacity-maximized transactions
    async generateDeliveryQueue() {
        const drivers = await this.getAvailableDrivers();
        const locations = await this.getUnassignedDeliveries();
        const deliveryQueue = [];
        if (locations.length === 0) {
            return deliveryQueue;
        }
        // Group locations by PO
        const poGroups = new Map();
        locations.forEach(loc => {
            if (loc.poId) {
                if (!poGroups.has(loc.poId)) {
                    poGroups.set(loc.poId, []);
                }
                poGroups.get(loc.poId).push(loc);
            }
        });
        let transactionId = 1;
        const driverTripCounts = new Map();
        drivers.forEach(driver => {
            driverTripCounts.set(driver.id, 0);
        });
        // For each PO and each delivery, create a trip for the remaining quantity
        for (const [poId, poLocations] of poGroups.entries()) {
            const deliveries = poLocations.filter(loc => loc.type === 'delivery');
            for (const delivery of deliveries) {
                let remainingQuantity = delivery.quantity || 0;
                // Keep creating transactions until all quantity is assigned
                while (remainingQuantity > 0) {
                    // Find the driver with the least trips (round-robin distribution)
                    const sortedDrivers = drivers.sort((a, b) => {
                        const aTrips = driverTripCounts.get(a.id) || 0;
                        const bTrips = driverTripCounts.get(b.id) || 0;
                        if (aTrips !== bTrips)
                            return aTrips - bTrips;
                        // If same number of trips, choose by distance to delivery
                        const distA = this.calculateDistance(a.currentLocation.latitude, a.currentLocation.longitude, delivery.latitude, delivery.longitude);
                        const distB = this.calculateDistance(b.currentLocation.latitude, b.currentLocation.longitude, delivery.latitude, delivery.longitude);
                        return distA - distB;
                    });
                    const selectedDriver = sortedDrivers[0];
                    if (!selectedDriver) {
                        console.warn('No available driver found for delivery');
                        break;
                    }
                    const driverCapacity = selectedDriver.capacity;
                    const assignedQuantity = Math.min(remainingQuantity, driverCapacity);
                    // Calculate distance and time for this trip
                    const distance = this.calculateDistance(selectedDriver.currentLocation.latitude, selectedDriver.currentLocation.longitude, delivery.latitude, delivery.longitude);
                    const estimatedTime = this.estimateTravelTime(distance);
                    // Create delivery transaction
                    const tripNumber = (driverTripCounts.get(selectedDriver.id) || 0) + 1;
                    driverTripCounts.set(selectedDriver.id, tripNumber);
                    const transaction = {
                        id: `TXN-${transactionId++}`,
                        driverId: selectedDriver.id,
                        driverName: selectedDriver.username,
                        poId,
                        deliveryLocation: delivery.address,
                        materials: [{
                                materialId: delivery.materialId || 'unknown',
                                materialName: delivery.loadType || 'Material',
                                quantity: assignedQuantity,
                                loadType: delivery.loadType || 'tons'
                            }],
                        totalQuantity: assignedQuantity,
                        tripNumber,
                        estimatedDistance: distance,
                        estimatedTime
                    };
                    deliveryQueue.push(transaction);
                    remainingQuantity -= assignedQuantity;
                }
            }
        }
        return deliveryQueue;
    }
    // Simple greedy VRP solver (can be replaced with more sophisticated algorithms)
    async solveVRP(options) {
        const startTime = Date.now();
        // Apply options with defaults
        const config = {
            ignoreCapacity: options?.ignoreCapacity || false,
            ignoreMaxDeliveries: options?.ignoreMaxDeliveries || false,
            capacityMultiplier: options?.capacityMultiplier || 1,
            maxDeliveriesOverride: options?.maxDeliveriesOverride
        };
        const drivers = await this.getAvailableDrivers();
        const locations = await this.getUnassignedDeliveries();
        if (drivers.length === 0) {
            throw new Error('No available drivers found');
        }
        if (locations.length === 0) {
            return {
                solutions: [],
                deliveryQueue: [],
                totalDistance: 0,
                totalTime: 0,
                unassignedOrders: [],
                unassignedReasons: {},
                optimizationStats: {
                    processingTime: Date.now() - startTime,
                    ordersProcessed: 0,
                    driversUsed: 0,
                    efficiency: 100
                }
            };
        }
        // Generate the delivery queue
        const deliveryQueue = await this.generateDeliveryQueue();
        const solutions = [];
        const assignedLocationIds = new Set();
        const unassignedOrders = [];
        const unassignedReasons = {};
        // Group locations by PO for pickup-delivery pairs
        const poGroups = new Map();
        locations.forEach(loc => {
            if (loc.poId) {
                if (!poGroups.has(loc.poId)) {
                    poGroups.set(loc.poId, []);
                }
                poGroups.get(loc.poId).push(loc);
            }
        });
        // New improved algorithm: Distribute orders evenly and split large loads
        const driverAssignments = new Map();
        // Initialize driver assignments
        drivers.forEach(driver => {
            driverAssignments.set(driver.id, {
                driver,
                route: [],
                currentLoad: 0,
                deliveryCount: 0,
                totalDistance: 0,
                totalTime: 0,
                currentLat: driver.currentLocation.latitude,
                currentLng: driver.currentLocation.longitude
            });
        });
        // Process each PO
        for (const [poId, poLocations] of poGroups.entries()) {
            const pickup = poLocations.find(loc => loc.type === 'pickup');
            const deliveries = poLocations.filter(loc => loc.type === 'delivery');
            if (!pickup || deliveries.length === 0) {
                unassignedReasons[poId] = 'Missing pickup or delivery locations';
                unassignedOrders.push(poId);
                continue;
            }
            // Calculate total quantity for this PO
            const totalQuantity = deliveries.reduce((sum, del) => sum + (del.quantity || 0), 0);
            // Find available drivers (not at max deliveries)
            const availableDrivers = Array.from(driverAssignments.values()).filter(assignment => {
                const effectiveMaxDeliveries = config.maxDeliveriesOverride || assignment.driver.maxDeliveries;
                return config.ignoreMaxDeliveries || assignment.deliveryCount < effectiveMaxDeliveries;
            });
            if (availableDrivers.length === 0) {
                unassignedReasons[poId] = 'No available drivers (all at maximum deliveries)';
                unassignedOrders.push(poId);
                continue;
            }
            // Sort drivers by current workload (least busy first) and distance to pickup
            // Prioritize completing all orders over perfect distribution
            availableDrivers.sort((a, b) => {
                // First priority: drivers with available capacity
                const aCapacity = (a.driver.capacity * config.capacityMultiplier) - a.currentLoad;
                const bCapacity = (b.driver.capacity * config.capacityMultiplier) - b.currentLoad;
                if (config.ignoreCapacity) {
                    // If ignoring capacity, just sort by workload and distance
                    const workloadDiff = a.deliveryCount - b.deliveryCount;
                    if (workloadDiff !== 0)
                        return workloadDiff;
                }
                else {
                    // Prioritize drivers with more available capacity
                    const capacityDiff = bCapacity - aCapacity;
                    if (Math.abs(capacityDiff) > 1)
                        return capacityDiff > 0 ? 1 : -1;
                }
                const distA = this.calculateDistance(a.currentLat, a.currentLng, pickup.latitude, pickup.longitude);
                const distB = this.calculateDistance(b.currentLat, b.currentLng, pickup.latitude, pickup.longitude);
                return distA - distB;
            });
            // Check if we need to split the load
            const firstDriver = availableDrivers[0];
            if (!firstDriver) {
                unassignedReasons[poId] = 'No available drivers found';
                unassignedOrders.push(poId);
                continue;
            }
            const effectiveCapacity = firstDriver.driver.capacity * config.capacityMultiplier;
            const needsLoadSplitting = !config.ignoreCapacity && totalQuantity > effectiveCapacity;
            if (needsLoadSplitting) {
                // Split load among multiple drivers
                let remainingQuantity = totalQuantity;
                const driversForThisOrder = [];
                // Determine how many drivers we need
                for (const driverAssignment of availableDrivers) {
                    if (remainingQuantity <= 0)
                        break;
                    const driverCapacity = driverAssignment.driver.capacity * config.capacityMultiplier;
                    const availableCapacity = driverCapacity - driverAssignment.currentLoad;
                    if (availableCapacity > 0) {
                        driversForThisOrder.push(driverAssignment);
                        remainingQuantity -= Math.min(availableCapacity, remainingQuantity);
                    }
                }
                if (driversForThisOrder.length === 0) {
                    unassignedReasons[poId] = 'No drivers with available capacity';
                    unassignedOrders.push(poId);
                    continue;
                }
                // Assign split loads to drivers
                remainingQuantity = totalQuantity;
                for (const driverAssignment of driversForThisOrder) {
                    if (remainingQuantity <= 0)
                        break;
                    const driverCapacity = driverAssignment.driver.capacity * config.capacityMultiplier;
                    const availableCapacity = driverCapacity - driverAssignment.currentLoad;
                    const assignedQuantity = Math.min(availableCapacity, remainingQuantity);
                    if (assignedQuantity > 0) {
                        // Add pickup to route if not already added
                        const hasPickup = driverAssignment.route.some(loc => loc.id === pickup.id);
                        if (!hasPickup) {
                            const distanceToPickup = this.calculateDistance(driverAssignment.currentLat, driverAssignment.currentLng, pickup.latitude, pickup.longitude);
                            driverAssignment.route.push(pickup);
                            assignedLocationIds.add(pickup.id);
                            driverAssignment.totalDistance += distanceToPickup;
                            driverAssignment.totalTime += this.estimateTravelTime(distanceToPickup);
                            driverAssignment.currentLat = pickup.latitude;
                            driverAssignment.currentLng = pickup.longitude;
                        }
                        // Add proportional deliveries
                        const proportionOfLoad = assignedQuantity / totalQuantity;
                        for (const delivery of deliveries) {
                            const deliveryQuantity = Math.ceil((delivery.quantity || 0) * proportionOfLoad);
                            if (deliveryQuantity > 0) {
                                // Create a modified delivery location with adjusted quantity
                                const adjustedDelivery = {
                                    ...delivery,
                                    id: `${delivery.id}_${driverAssignment.driver.id}`,
                                    quantity: deliveryQuantity
                                };
                                const distance = this.calculateDistance(driverAssignment.currentLat, driverAssignment.currentLng, delivery.latitude, delivery.longitude);
                                driverAssignment.route.push(adjustedDelivery);
                                assignedLocationIds.add(adjustedDelivery.id);
                                driverAssignment.totalDistance += distance;
                                driverAssignment.totalTime += this.estimateTravelTime(distance);
                                driverAssignment.currentLat = delivery.latitude;
                                driverAssignment.currentLng = delivery.longitude;
                                driverAssignment.deliveryCount++;
                            }
                        }
                        driverAssignment.currentLoad += assignedQuantity;
                        remainingQuantity -= assignedQuantity;
                    }
                }
                if (remainingQuantity > 0) {
                    unassignedReasons[poId] = `Partially assigned - ${remainingQuantity} units remaining (insufficient total capacity)`;
                }
            }
            else {
                // Assign entire order to the best available driver
                const bestDriver = availableDrivers[0];
                if (!bestDriver) {
                    unassignedReasons[poId] = 'No available drivers found';
                    unassignedOrders.push(poId);
                    continue;
                }
                // Check capacity if not ignored
                if (!config.ignoreCapacity) {
                    const effectiveCapacity = bestDriver.driver.capacity * config.capacityMultiplier;
                    const availableCapacity = effectiveCapacity - bestDriver.currentLoad;
                    if (totalQuantity > availableCapacity) {
                        unassignedReasons[poId] = `Exceeds available capacity (${totalQuantity} > ${availableCapacity})`;
                        unassignedOrders.push(poId);
                        continue;
                    }
                }
                // Add pickup to route
                const distanceToPickup = this.calculateDistance(bestDriver.currentLat, bestDriver.currentLng, pickup.latitude, pickup.longitude);
                bestDriver.route.push(pickup);
                assignedLocationIds.add(pickup.id);
                bestDriver.totalDistance += distanceToPickup;
                bestDriver.totalTime += this.estimateTravelTime(distanceToPickup);
                bestDriver.currentLat = pickup.latitude;
                bestDriver.currentLng = pickup.longitude;
                // Add deliveries sorted by distance
                const sortedDeliveries = deliveries.sort((a, b) => {
                    const distA = this.calculateDistance(bestDriver.currentLat, bestDriver.currentLng, a.latitude, a.longitude);
                    const distB = this.calculateDistance(bestDriver.currentLat, bestDriver.currentLng, b.latitude, b.longitude);
                    return distA - distB;
                });
                for (const delivery of sortedDeliveries) {
                    const distance = this.calculateDistance(bestDriver.currentLat, bestDriver.currentLng, delivery.latitude, delivery.longitude);
                    bestDriver.route.push(delivery);
                    assignedLocationIds.add(delivery.id);
                    bestDriver.totalDistance += distance;
                    bestDriver.totalTime += this.estimateTravelTime(distance);
                    bestDriver.currentLat = delivery.latitude;
                    bestDriver.currentLng = delivery.longitude;
                    bestDriver.deliveryCount++;
                }
                bestDriver.currentLoad += totalQuantity;
            }
        }
        // Convert driver assignments to solutions
        for (const assignment of driverAssignments.values()) {
            if (assignment.route.length > 0) {
                const assignments = await this.generateAssignments(assignment.route);
                solutions.push({
                    vehicleId: assignment.driver.id,
                    userId: assignment.driver.userId,
                    username: assignment.driver.username,
                    route: assignment.route,
                    totalDistance: assignment.totalDistance,
                    totalTime: assignment.totalTime,
                    totalLoad: assignment.currentLoad,
                    assignments
                });
            }
        }
        // Find unassigned orders
        poGroups.forEach((locs, poId) => {
            if (!locs.some(loc => assignedLocationIds.has(loc.id))) {
                unassignedOrders.push(poId);
                if (!unassignedReasons[poId]) {
                    unassignedReasons[poId] = 'No available drivers or all drivers at capacity';
                }
            }
        });
        const totalDistance = solutions.reduce((sum, sol) => sum + sol.totalDistance, 0);
        const totalTime = solutions.reduce((sum, sol) => sum + sol.totalTime, 0);
        return {
            solutions,
            deliveryQueue,
            totalDistance,
            totalTime,
            unassignedOrders,
            unassignedReasons,
            optimizationStats: {
                processingTime: Date.now() - startTime,
                ordersProcessed: poGroups.size - unassignedOrders.length,
                driversUsed: solutions.length,
                efficiency: poGroups.size > 0 ? ((poGroups.size - unassignedOrders.length) / poGroups.size) * 100 : 100
            }
        };
    }
    // Generate assignment objects from route
    async generateAssignments(route) {
        const assignments = [];
        const poIds = [...new Set(route.filter(loc => loc.poId).map(loc => loc.poId))];
        for (const poId of poIds) {
            const po = await PurchaseOrder_1.PurchaseOrder.findById(poId);
            if (!po)
                continue;
            const deliveryLocations = route.filter(loc => loc.poId === poId && loc.type === 'delivery');
            // Group deliveries by location (in case of split loads)
            const locationGroups = new Map();
            deliveryLocations.forEach(loc => {
                const baseAddress = loc.address.replace(/_.*$/, ''); // Remove driver suffix if present
                if (!locationGroups.has(baseAddress)) {
                    locationGroups.set(baseAddress, []);
                }
                locationGroups.get(baseAddress).push(loc);
            });
            for (const [baseAddress, locations] of locationGroups.entries()) {
                // Find matching load in PO
                const matchingLoad = po.loads?.find(load => {
                    if (!load.deliveryLocation)
                        return false;
                    const loadAddress = `${load.deliveryLocation.street}, ${load.deliveryLocation.city}, ${load.deliveryLocation.state}`;
                    return loadAddress === baseAddress || (locations.length > 0 && locations[0] && loadAddress === locations[0].address);
                });
                if (matchingLoad) {
                    // Calculate total assigned quantity for this location from all split loads
                    const totalAssignedQuantity = locations.reduce((sum, loc) => sum + (loc.quantity || 0), 0);
                    // Create materials with proportional quantities
                    const materials = matchingLoad.materials.map(mat => {
                        const originalQuantity = mat.quantity;
                        const totalLocationQuantity = matchingLoad.materials.reduce((sum, m) => sum + m.quantity, 0);
                        const proportionalQuantity = totalLocationQuantity > 0
                            ? Math.ceil((originalQuantity / totalLocationQuantity) * totalAssignedQuantity)
                            : originalQuantity;
                        return {
                            materialId: mat.materialId.toString(),
                            materialName: mat.materialName,
                            quantity: proportionalQuantity,
                            loadType: mat.loadType
                        };
                    });
                    assignments.push({
                        poId,
                        deliveryLocationName: baseAddress,
                        materials
                    });
                }
            }
        }
        return assignments;
    }
    // Clear existing reserved transactions
    async clearReservedTransactions() {
        console.log('[clearReservedTransactions] Clearing existing reserved transactions...');
        try {
            const pos = await PurchaseOrder_1.PurchaseOrder.find({
                'transactions.status': 'Reserved'
            });
            for (const po of pos) {
                // Remove only Reserved transactions, keep Completed ones
                po.transactions = po.transactions.filter(txn => txn.status !== 'Reserved');
                await po.save();
            }
            console.log(`[clearReservedTransactions] Cleared reserved transactions from ${pos.length} POs`);
        }
        catch (error) {
            console.error('[clearReservedTransactions] Error clearing reserved transactions:', error);
        }
    }
    // Apply VRP solution by creating assignments from the delivery queue
    async applySolution(solution) {
        // Prevent concurrent calls
        if (this.isApplyingSolution) {
            console.log('[applySolution] Already applying solution, skipping...');
            return;
        }
        this.isApplyingSolution = true;
        try {
            // Use the delivery queue instead of the old solution format
            if (!solution.deliveryQueue || solution.deliveryQueue.length === 0) {
                console.log('[applySolution] No delivery queue found in solution');
                return;
            }
            // Clear existing reserved transactions first
            await this.clearReservedTransactions();
            console.log(`[applySolution] Processing ${solution.deliveryQueue.length} delivery transactions`);
            // Group transactions by PO to batch updates
            const transactionsByPO = new Map();
            for (const transaction of solution.deliveryQueue) {
                if (!transactionsByPO.has(transaction.poId)) {
                    transactionsByPO.set(transaction.poId, []);
                }
                transactionsByPO.get(transaction.poId).push(transaction);
            }
            // Process each PO once with all its transactions
            for (const [poId, transactions] of transactionsByPO.entries()) {
                console.log(`[applySolution] Processing PO ${poId} with ${transactions.length} transactions`);
                try {
                    const po = await PurchaseOrder_1.PurchaseOrder.findById(poId);
                    if (!po) {
                        console.warn(`[applySolution] PO not found: ${poId}`);
                        continue;
                    }
                    // Add all transactions for this PO at once
                    for (const transaction of transactions) {
                        console.log(`[applySolution] Adding transaction ${transaction.id} for driver: ${transaction.driverName}, Quantity: ${transaction.totalQuantity}`);
                        po.transactions.push({
                            truckerId: new mongoose_1.default.Types.ObjectId(transaction.driverId),
                            status: 'Reserved',
                            reservedAt: new Date(),
                            reservedMaterials: transaction.materials.map(mat => ({
                                materialId: mat.materialId,
                                materialName: mat.materialName,
                                quantity: mat.quantity,
                                deliveryLocationName: transaction.deliveryLocation
                            })),
                            assignmentSource: 'auto',
                            isApproved: true, // Auto-dispatch is immediately approved for backward compatibility
                            lastModified: new Date(),
                            originalAssignmentId: transaction.id
                        });
                    }
                    // Save once per PO
                    await po.save();
                    console.log(`[applySolution] Successfully saved ${transactions.length} transactions for PO: ${poId}`);
                }
                catch (error) {
                    console.error(`[applySolution] Failed to apply transactions for PO ${poId}:`, error);
                }
            }
        }
        finally {
            this.isApplyingSolution = false;
        }
    }
}
exports.default = new VRPService();
//# sourceMappingURL=vrpService.js.map