"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.verifyToken = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const verifyToken = (req, res, next) => {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
        return res.status(401).json({ message: 'No token provided' });
    }
    const token = authHeader.split(' ')[1];
    if (!token) {
        return res.status(401).json({ message: 'No token provided' });
    }
    const jwtSecret = process.env.JWT_SECRET;
    if (!jwtSecret) {
        return res.status(500).json({ message: 'JWT_SECRET environment variable not set' });
    }
    try {
        const decoded = jsonwebtoken_1.default.verify(token, jwtSecret);
        if (typeof decoded === 'object' && decoded !== null && 'id' in decoded && 'role' in decoded) {
            req.user = { id: decoded.id, role: decoded.role };
            return next();
        }
        else {
            return res.status(401).json({ message: 'Invalid token payload' });
        }
    }
    catch (error) {
        return res.status(401).json({ message: 'Invalid token' });
    }
};
exports.verifyToken = verifyToken;
//# sourceMappingURL=authMiddleware.js.map