"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PendingAssignment = void 0;
const mongoose_1 = __importStar(require("mongoose"));
const PendingAssignmentSchema = new mongoose_1.Schema({
    vrpSolutionId: { type: String, required: true },
    poId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'PurchaseOrder', required: true },
    truckerId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'User', required: true },
    deliveryLocation: { type: String, required: true },
    materials: [{
            materialId: { type: mongoose_1.Schema.Types.Mixed, required: true },
            materialName: { type: String, required: true },
            quantity: { type: Number, required: true },
            loadType: { type: String, required: true }
        }],
    totalQuantity: { type: Number, required: true },
    tripNumber: { type: Number, required: true },
    estimatedDistance: { type: Number, required: true },
    estimatedTime: { type: Number, required: true },
    assignmentSource: { type: String, enum: ['auto', 'manual'], required: true },
    createdAt: { type: Date, default: Date.now },
    createdBy: { type: mongoose_1.Schema.Types.ObjectId, ref: 'User', required: true },
    status: { type: String, enum: ['pending', 'approved', 'rejected', 'modified'], default: 'pending' },
    rejectionReason: { type: String },
    originalAssignmentId: { type: String }
});
// Index for efficient queries
PendingAssignmentSchema.index({ vrpSolutionId: 1, status: 1 });
PendingAssignmentSchema.index({ truckerId: 1, status: 1 });
PendingAssignmentSchema.index({ poId: 1, status: 1 });
exports.PendingAssignment = mongoose_1.default.model('PendingAssignment', PendingAssignmentSchema);
//# sourceMappingURL=PendingAssignment.js.map