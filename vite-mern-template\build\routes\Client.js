"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const Client_1 = __importDefault(require("../models/Client"));
const router = express_1.default.Router();
// Get all clients
router.get('/', async (req, res) => {
    try {
        const clients = await Client_1.default.find();
        res.json(clients);
    }
    catch (err) {
        res.status(500).json({ error: 'Failed to fetch clients' });
    }
});
// Get a single client by ID
router.get('/:id', async (req, res) => {
    try {
        const client = await Client_1.default.findById(req.params.id);
        if (!client)
            return res.status(404).json({ error: 'Client not found' });
        res.json(client);
    }
    catch (err) {
        res.status(500).json({ error: 'Failed to fetch client' });
    }
});
// Create a new client
router.post('/', async (req, res) => {
    try {
        const client = new Client_1.default(req.body);
        await client.save();
        res.status(201).json(client);
    }
    catch (err) {
        res.status(400).json({ error: 'Failed to create client', details: err });
    }
});
// Update a client
router.put('/:id', async (req, res) => {
    try {
        const client = await Client_1.default.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!client)
            return res.status(404).json({ error: 'Client not found' });
        res.json(client);
    }
    catch (err) {
        res.status(400).json({ error: 'Failed to update client', details: err });
    }
});
// Delete a client
router.delete('/:id', async (req, res) => {
    try {
        const client = await Client_1.default.findByIdAndDelete(req.params.id);
        if (!client)
            return res.status(404).json({ error: 'Client not found' });
        res.json({ message: 'Client deleted' });
    }
    catch (err) {
        res.status(500).json({ error: 'Failed to delete client' });
    }
});
exports.default = router;
//# sourceMappingURL=Client.js.map