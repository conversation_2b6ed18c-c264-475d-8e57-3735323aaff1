"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const router = express_1.default.Router();
// Geocoding endpoint
router.post('/', async (req, res) => {
    try {
        const { address } = req.body;
        if (!address) {
            return res.status(400).json({ error: 'Address is required' });
        }
        // Use a more reliable geocoding service
        const encodedAddress = encodeURIComponent(address);
        // Try multiple geocoding services for better reliability
        let geocodingResult = null;
        // First try: OpenStreetMap Nominatim
        try {
            const nominatimUrl = `https://nominatim.openstreetmap.org/search?format=json&q=${encodedAddress}&limit=1&addressdetails=1`;
            const nominatimResponse = await fetch(nominatimUrl, {
                headers: {
                    'User-Agent': 'SelfMade-App/1.0'
                }
            });
            if (nominatimResponse.ok) {
                const data = await nominatimResponse.json();
                if (data && Array.isArray(data) && data.length > 0) {
                    const firstResult = data[0];
                    if (firstResult && firstResult.lat && firstResult.lon) {
                        geocodingResult = {
                            lat: parseFloat(firstResult.lat),
                            lng: parseFloat(firstResult.lon),
                            display_name: firstResult.display_name,
                            address: firstResult.address
                        };
                    }
                }
            }
        }
        catch (error) {
            console.log('Nominatim failed, trying alternative...');
        }
        // Fallback: Try a different geocoding service
        if (!geocodingResult) {
            try {
                const geocodeUrl = `https://geocode.maps.co/search?q=${encodedAddress}`;
                const geocodeResponse = await fetch(geocodeUrl);
                if (geocodeResponse.ok) {
                    const data = await geocodeResponse.json();
                    if (data && Array.isArray(data) && data.length > 0) {
                        const firstResult = data[0];
                        if (firstResult && firstResult.lat && firstResult.lon) {
                            geocodingResult = {
                                lat: parseFloat(firstResult.lat),
                                lng: parseFloat(firstResult.lon),
                                display_name: firstResult.display_name || address,
                                address: firstResult.address
                            };
                        }
                    }
                }
            }
            catch (error) {
                console.log('Alternative geocoding failed');
            }
        }
        if (geocodingResult) {
            res.json(geocodingResult);
        }
        else {
            res.status(404).json({ error: 'Location not found. Please try a different address or check the spelling.' });
        }
    }
    catch (error) {
        console.error('Geocoding error:', error);
        res.status(500).json({ error: 'Failed to geocode address. Please try again.' });
    }
});
// Search locations endpoint for autocomplete
router.get('/search', async (req, res) => {
    try {
        const { q } = req.query;
        if (!q || typeof q !== 'string') {
            return res.status(400).json({ error: 'Query parameter is required' });
        }
        const encodedQuery = encodeURIComponent(q);
        const url = `https://nominatim.openstreetmap.org/search?format=json&q=${encodedQuery}&limit=5&addressdetails=1`;
        const response = await fetch(url, {
            headers: {
                'User-Agent': 'SelfMade-App/1.0'
            }
        });
        if (response.ok) {
            const data = await response.json();
            const results = data.map((item) => ({
                display_name: item.display_name,
                lat: parseFloat(item.lat),
                lng: parseFloat(item.lon),
                type: 'location',
                importance: 0.5,
                address: item.address
            }));
            res.json(results);
        }
        else {
            res.status(500).json({ error: 'Failed to search locations' });
        }
    }
    catch (error) {
        console.error('Location search error:', error);
        res.status(500).json({ error: 'Failed to search locations' });
    }
});
exports.default = router;
//# sourceMappingURL=geocode.js.map