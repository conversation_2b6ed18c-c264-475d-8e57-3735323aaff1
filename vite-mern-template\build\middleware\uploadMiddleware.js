"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const multer_1 = __importDefault(require("multer"));
const storage = multer_1.default.memoryStorage(); // or use diskStorage if you want to save to disk
const upload = (0, multer_1.default)({
    storage,
    // No file size limit here; resizing will be handled in route logic
});
exports.default = upload;
//# sourceMappingURL=uploadMiddleware.js.map