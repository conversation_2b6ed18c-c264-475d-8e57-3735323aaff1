"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const Job_1 = __importDefault(require("../models/Job"));
const router = express_1.default.Router();
// Get all jobs, optionally filter by clientId
router.get('/', async (req, res) => {
    try {
        const filter = {};
        if (req.query.clientId) {
            filter.clientId = req.query.clientId;
        }
        const jobs = await Job_1.default.find(filter);
        res.json(jobs);
    }
    catch (err) {
        res.status(500).json({ error: 'Failed to fetch jobs' });
    }
});
// Get a single job by ID
router.get('/:id', async (req, res) => {
    try {
        const job = await Job_1.default.findById(req.params.id);
        if (!job)
            return res.status(404).json({ error: 'Job not found' });
        res.json(job);
    }
    catch (err) {
        res.status(500).json({ error: 'Failed to fetch job' });
    }
});
// Create a new job
router.post('/', async (req, res) => {
    try {
        const job = new Job_1.default(req.body);
        await job.save();
        res.status(201).json(job);
    }
    catch (err) {
        res.status(400).json({ error: 'Failed to create job', details: err });
    }
});
// Update a job
router.put('/:id', async (req, res) => {
    try {
        const job = await Job_1.default.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!job)
            return res.status(404).json({ error: 'Job not found' });
        res.json(job);
    }
    catch (err) {
        res.status(400).json({ error: 'Failed to update job', details: err });
    }
});
// Delete a job
router.delete('/:id', async (req, res) => {
    try {
        const job = await Job_1.default.findByIdAndDelete(req.params.id);
        if (!job)
            return res.status(404).json({ error: 'Job not found' });
        res.json({ message: 'Job deleted' });
    }
    catch (err) {
        res.status(500).json({ error: 'Failed to delete job' });
    }
});
exports.default = router;
//# sourceMappingURL=Job.js.map