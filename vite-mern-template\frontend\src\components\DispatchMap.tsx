import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-leaflet';
import L from 'leaflet';
import 'leaflet/dist/leaflet.css';
import { Card, CardContent } from "@/components/ui/card";
import axios from 'axios';

// Fix for default markers in Leaflet
delete (L.Icon.Default.prototype as any)._getIconUrl;
L.Icon.Default.mergeOptions({
  iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
  iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
});

// Custom icons for different location types
const pickupIcon = new L.Icon({
  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-green.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const deliveryIcon = new L.Icon({
  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-red.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

const driverIcon = new L.Icon({
  iconUrl: 'https://raw.githubusercontent.com/pointhi/leaflet-color-markers/master/img/marker-icon-2x-blue.png',
  shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41]
});

interface MapLocation {
  id: string;
  latitude: number;
  longitude: number;
  address: string;
  type: 'pickup' | 'delivery' | 'driver';
  name: string;
  details?: string;
  driverId?: string;
  poId?: string;
}

interface RouteData {
  driverId: string;
  driverName: string;
  locations: MapLocation[];
  routeColor: string;
}

const DispatchMap: React.FC = () => {
  const [locations, setLocations] = useState<MapLocation[]>([]);
  const [routes, setRoutes] = useState<RouteData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedDriver, setSelectedDriver] = useState<string>('all');

  // Default center (can be adjusted based on your area)
  const defaultCenter: [number, number] = [40.7128, -74.0060]; // New York City

  useEffect(() => {
    fetchMapData();
  }, []);

  const fetchMapData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('token');
      const headers = { Authorization: `Bearer ${token}` };

      // Fetch current delivery queue for map visualization
      const queueResponse = await axios.get('/api/dispatch/queue', { headers });
      const deliveryQueue = queueResponse.data.deliveryQueue || [];

      // Fetch pending assignments
      const pendingResponse = await axios.get('/api/dispatch/pending', { headers });
      const pendingAssignments = pendingResponse.data.pendingAssignments || [];

      // Convert data to map locations and routes
      const mapLocations: MapLocation[] = [];
      const routeData: RouteData[] = [];
      const driverRoutes = new Map<string, MapLocation[]>();

      // Process delivery queue (approved assignments)
      deliveryQueue.forEach((transaction: any, index: number) => {
        const deliveryLocation: MapLocation = {
          id: `delivery-${transaction.id}`,
          latitude: 40.7128 + (Math.random() - 0.5) * 0.1, // Mock coordinates
          longitude: -74.0060 + (Math.random() - 0.5) * 0.1,
          address: transaction.deliveryLocation,
          type: 'delivery',
          name: `Delivery ${index + 1}`,
          details: `${transaction.totalQuantity} tons - ${transaction.driverName}`,
          driverId: transaction.driverId,
          poId: transaction.poId
        };

        mapLocations.push(deliveryLocation);

        // Group by driver for routes
        if (!driverRoutes.has(transaction.driverId)) {
          driverRoutes.set(transaction.driverId, []);
        }
        driverRoutes.get(transaction.driverId)?.push(deliveryLocation);
      });

      // Process pending assignments
      pendingAssignments.forEach((assignment: any, index: number) => {
        const pendingLocation: MapLocation = {
          id: `pending-${assignment._id}`,
          latitude: 40.7128 + (Math.random() - 0.5) * 0.1, // Mock coordinates
          longitude: -74.0060 + (Math.random() - 0.5) * 0.1,
          address: assignment.deliveryLocation,
          type: 'delivery',
          name: `Pending ${index + 1}`,
          details: `${assignment.totalQuantity} tons - ${assignment.truckerId.username} (PENDING)`,
          driverId: assignment.truckerId._id,
          poId: assignment.poId._id
        };

        mapLocations.push(pendingLocation);
      });

      // Create route data
      const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
      let colorIndex = 0;

      driverRoutes.forEach((locations, driverId) => {
        if (locations.length > 0) {
          routeData.push({
            driverId,
            driverName: locations[0].details?.split(' - ')[1] || 'Unknown Driver',
            locations,
            routeColor: colors[colorIndex % colors.length]
          });
          colorIndex++;
        }
      });

      setLocations(mapLocations);
      setRoutes(routeData);
    } catch (error: any) {
      setError(error.response?.data?.error || 'Failed to fetch map data');
    } finally {
      setIsLoading(false);
    }
  };

  const filteredRoutes = selectedDriver === 'all' 
    ? routes 
    : routes.filter(route => route.driverId === selectedDriver);

  const filteredLocations = selectedDriver === 'all'
    ? locations
    : locations.filter(loc => loc.driverId === selectedDriver);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">🗺️ Dispatch Map View</h2>
        <div className="flex space-x-3">
          <select
            value={selectedDriver}
            onChange={(e) => setSelectedDriver(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Drivers</option>
            {routes.map(route => (
              <option key={route.driverId} value={route.driverId}>
                {route.driverName}
              </option>
            ))}
          </select>
          <button
            onClick={fetchMapData}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                <span>Loading...</span>
              </>
            ) : (
              <>
                <span>🔄</span>
                <span>Refresh</span>
              </>
            )}
          </button>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
          {error}
        </div>
      )}

      {/* Map */}
      <Card>
        <CardContent className="p-0">
          <div className="h-96 w-full">
            <MapContainer
              center={defaultCenter}
              zoom={12}
              style={{ height: '100%', width: '100%' }}
            >
              <TileLayer
                url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              />
              
              {/* Render locations */}
              {filteredLocations.map((location) => (
                <Marker
                  key={location.id}
                  position={[location.latitude, location.longitude]}
                  icon={location.type === 'pickup' ? pickupIcon : 
                        location.type === 'delivery' ? deliveryIcon : driverIcon}
                >
                  <Popup>
                    <div>
                      <h3 className="font-semibold">{location.name}</h3>
                      <p className="text-sm">{location.address}</p>
                      {location.details && (
                        <p className="text-xs text-gray-600 mt-1">{location.details}</p>
                      )}
                    </div>
                  </Popup>
                </Marker>
              ))}

              {/* Render routes */}
              {filteredRoutes.map((route) => {
                const routeCoordinates: [number, number][] = route.locations.map(loc => [loc.latitude, loc.longitude]);
                return (
                  <Polyline
                    key={route.driverId}
                    positions={routeCoordinates}
                    color={route.routeColor}
                    weight={3}
                    opacity={0.7}
                  />
                );
              })}
            </MapContainer>
          </div>
        </CardContent>
      </Card>

      {/* Legend */}
      <Card>
        <CardContent className="p-4">
          <h3 className="font-semibold mb-3">Map Legend</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-green-500 rounded-full"></div>
              <span className="text-sm">Pickup Locations</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-red-500 rounded-full"></div>
              <span className="text-sm">Delivery Locations</span>
            </div>
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 bg-blue-500 rounded-full"></div>
              <span className="text-sm">Driver Locations</span>
            </div>
          </div>
          <div className="mt-3 text-xs text-gray-600">
            <p>• Colored lines represent driver routes</p>
            <p>• Click markers for detailed information</p>
            <p>• Use driver filter to focus on specific routes</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default DispatchMap;
