"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PurchaseOrder = void 0;
const mongoose_1 = __importStar(require("mongoose"));
// --- Schemas ---
const MaterialSchema = new mongoose_1.Schema({
    name: { type: String, required: true },
    description: { type: String, required: true },
    cost: { type: Number, required: true },
    quantity: { type: Number, required: true },
}, { _id: true });
const DeliveryMaterialSchema = new mongoose_1.Schema({
    materialId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'Material', required: true },
    quantity: { type: Number, required: true },
}, { _id: false });
const DeliveryLocationSchema = new mongoose_1.Schema({
    name: { type: String, required: true },
    address: { type: String, required: true },
    materials: { type: [MaterialSchema], required: true },
}, { _id: false });
const TransactionSchema = new mongoose_1.Schema({
    truckerId: { type: mongoose_1.Schema.Types.ObjectId, ref: 'User', required: true },
    status: { type: String, enum: ['Reserved', 'PickedUp', 'Completed'], required: true },
    reservedAt: { type: Date, default: Date.now },
    pickedUpAt: { type: Date },
    completedAt: { type: Date },
    reservedMaterials: [{
            materialId: { type: mongoose_1.Schema.Types.Mixed, required: true },
            materialName: { type: String, required: true },
            quantity: { type: Number, required: true },
            deliveryLocationName: { type: String, required: true },
        }],
    // Manual dispatch tracking fields
    assignmentSource: { type: String, enum: ['auto', 'manual'], default: 'auto' },
    isApproved: { type: Boolean, default: false },
    approvedBy: { type: mongoose_1.Schema.Types.ObjectId, ref: 'User' },
    approvedAt: { type: Date },
    lastModified: { type: Date, default: Date.now },
    originalAssignmentId: { type: String },
    signature: { type: String },
    pickupTicketImage: { type: String },
    materialsPickedUp: [{
            materialId: { type: mongoose_1.Schema.Types.Mixed, required: true },
            quantity: { type: Number, required: true },
            deliveryLocationName: { type: String, required: true },
        }],
    deliverySignature: { type: String },
    deliveryTicketImage: { type: String },
    materialsDelivered: [{
            materialId: { type: mongoose_1.Schema.Types.Mixed, required: true },
            quantity: { type: Number, required: true },
            deliveryLocationName: { type: String, required: true },
        }]
}, { _id: true });
const LoadSchema = new mongoose_1.Schema({
    deliveryLocation: {
        street: { type: String, required: false },
        city: { type: String, required: false },
        state: { type: String, required: false },
        postalCode: { type: String, required: false },
        latitude: { type: Number, required: false },
        longitude: { type: Number, required: false }
    },
    materials: [{
            materialId: { type: Number, required: true },
            materialName: { type: String, required: true },
            quantity: { type: Number, required: true },
            loadType: { type: String, enum: ['tons', 'cubic_yards'], required: true },
        }]
}, { _id: false });
const PickupMaterialSchema = new mongoose_1.Schema({
    id: { type: Number, required: true },
    name: { type: String, required: true },
    quantity: { type: Number, required: true },
    loadType: { type: String, enum: ['tons', 'cubic_yards'], required: true },
}, { _id: false });
const PurchaseOrderSchema = new mongoose_1.Schema({
    clientName: { type: String, required: true },
    orderNumber: { type: Number, required: true, unique: true },
    jobName: { type: String },
    pickupLocation: {
        name: { type: String, required: true },
        address: { type: String, required: true },
        coordinates: {
            lat: { type: Number, required: true },
            lng: { type: Number, required: true },
        }
    },
    materials: { type: [PickupMaterialSchema], default: [] },
    loads: { type: [LoadSchema], required: true },
    transactions: { type: [TransactionSchema], default: [] },
    totalDeliveredQuantities: [{
            materialId: { type: mongoose_1.Schema.Types.Mixed, required: true },
            deliveryLocation: { type: String, required: true },
            quantity: { type: Number, required: true },
        }],
    isCompleted: { type: Boolean, default: false },
    createdAt: { type: Date, default: Date.now },
});
// --- Model ---
exports.PurchaseOrder = mongoose_1.default.models.PurchaseOrder ||
    mongoose_1.default.model('PurchaseOrder', PurchaseOrderSchema);
//# sourceMappingURL=PurchaseOrder.js.map