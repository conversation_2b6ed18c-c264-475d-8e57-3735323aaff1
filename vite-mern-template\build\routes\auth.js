"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const User_1 = __importDefault(require("../models/User"));
const argon2_1 = __importDefault(require("argon2"));
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const authMiddleware_1 = require("../middleware/authMiddleware");
const uploadMiddleware_1 = __importDefault(require("../middleware/uploadMiddleware"));
const path_1 = __importDefault(require("path"));
const fs_1 = __importDefault(require("fs"));
const email_1 = require("../utils/email");
const router = (0, express_1.Router)();
router.post('/login', async (req, res) => {
    const { username, password } = req.body;
    if (!username || !password) {
        return res.status(400).json({ message: 'Username and password required' });
    }
    try {
        const user = await User_1.default.findOne({ username });
        if (!user)
            return res.status(401).json({ message: 'Invalid credentials' });
        const valid = await argon2_1.default.verify(user.password, password);
        if (!valid)
            return res.status(401).json({ message: 'Invalid credentials' });
        // Create a JWT token
        const jwtSecret = process.env.JWT_SECRET;
        if (!jwtSecret) {
            return res.status(500).json({ message: 'JWT_SECRET not configured' });
        }
        const token = jsonwebtoken_1.default.sign({ id: user._id, role: user.role }, jwtSecret, { expiresIn: '1h' });
        return res.json({ message: 'Login successful', role: user.role, token });
    }
    catch (error) {
        console.error(error);
        return res.status(500).json({ message: 'Server error' });
    }
});
// List users (admin only, optional ?role=trucker&status=Active)
router.get('/users', authMiddleware_1.verifyToken, async (req, res) => {
    const user = req.user;
    if (!user || user.role !== 'admin')
        return res.status(403).json({ message: 'Forbidden' });
    const filter = {};
    if (req.query.role)
        filter.role = req.query.role;
    if (req.query.status)
        filter.status = req.query.status;
    try {
        const users = await User_1.default.find(filter).select('username email role licensePhotoUrl insurancePhotoUrl licenseExp insuranceExp status');
        res.json(users);
    }
    catch (err) {
        res.status(500).json({ message: 'Failed to fetch users' });
    }
});
// Create user (admin only)
router.post('/users', authMiddleware_1.verifyToken, async (req, res) => {
    const user = req.user;
    if (!user || user.role !== 'admin')
        return res.status(403).json({ message: 'Forbidden' });
    const { username, email, password, role, licenseExp, insuranceExp, status } = req.body;
    if (!username || !password || !role) {
        return res.status(400).json({ message: 'Username, password, and role are required' });
    }
    if (!['trucker', 'admin'].includes(role)) {
        return res.status(400).json({ message: 'Invalid role' });
    }
    try {
        const existing = await User_1.default.findOne({ username });
        if (existing)
            return res.status(400).json({ message: 'Username already exists' });
        const hashedPassword = await argon2_1.default.hash(password);
        const newUser = new User_1.default({ username, email: email || `${username}@example.com`, password: hashedPassword, role, licenseExp, insuranceExp, status });
        await newUser.save();
        // Send confirmation email to new truckers
        if (role === 'trucker' && newUser.email) {
            try {
                const platformUrl = process.env.PLATFORM_URL || 'https://your-platform-url.com';
                await (0, email_1.sendEmail)(newUser.email, 'Your Trucker Account is Ready', `Hello ${username},\n\nYour trucker account has been set up.\n\nLogin at: ${platformUrl}\nUsername: ${username}\nPassword: ${password}\nUser ID: ${newUser._id}\n\nPlease log in and upload your driver license and insurance card as soon as possible.\n\nThank you!`, `<p>Hello <b>${username}</b>,</p>
           <p>Your trucker account has been set up.</p>
           <ul>
             <li><b>Platform:</b> <a href="${platformUrl}">${platformUrl}</a></li>
             <li><b>Username:</b> ${username}</li>
             <li><b>Password:</b> ${password}</li>
             <li><b>User ID:</b> ${newUser._id}</li>
           </ul>
           <p>Please log in and upload your <b>driver license</b> and <b>insurance card</b> as soon as possible to complete your profile and be eligible for assignments.</p>
           <p>Thank you!</p>`);
            }
            catch (emailErr) {
                console.error('Failed to send trucker welcome email:', emailErr);
            }
        }
        res.status(201).json({ message: 'User created', user: { username: newUser.username, email: newUser.email, role: newUser.role, licenseExp: newUser.licenseExp, insuranceExp: newUser.insuranceExp, status: newUser.status } });
    }
    catch (err) {
        res.status(500).json({ message: 'Failed to create user' });
    }
});
// PATCH user (admin only)
router.patch('/users/:id', authMiddleware_1.verifyToken, async (req, res) => {
    const user = req.user;
    if (!user || user.role !== 'admin')
        return res.status(403).json({ message: 'Forbidden' });
    const { username, email, licenseExp, insuranceExp, status } = req.body;
    try {
        const updated = await User_1.default.findByIdAndUpdate(req.params.id, { $set: { username, email, licenseExp, insuranceExp, status } }, { new: true });
        if (!updated)
            return res.status(404).json({ message: 'User not found' });
        res.json({ message: 'User updated', user: updated });
    }
    catch (err) {
        res.status(500).json({ message: 'Failed to update user' });
    }
});
// Delete user (admin only)
router.delete('/users/:id', authMiddleware_1.verifyToken, async (req, res) => {
    const user = req.user;
    if (!user || user.role !== 'admin')
        return res.status(403).json({ message: 'Forbidden' });
    try {
        const deleted = await User_1.default.findByIdAndDelete(req.params.id);
        if (!deleted)
            return res.status(404).json({ message: 'User not found' });
        res.json({ message: 'User deleted' });
    }
    catch (err) {
        res.status(500).json({ message: 'Failed to delete user' });
    }
});
// Upload driver's license photo
router.post('/users/:id/license', authMiddleware_1.verifyToken, uploadMiddleware_1.default.single('license'), async (req, res) => {
    const user = req.user;
    const { id } = req.params;
    if (!user || (user.role !== 'admin' && user.id !== id))
        return res.status(403).json({ message: 'Forbidden' });
    if (!req.file)
        return res.status(400).json({ message: 'No file uploaded' });
    const ext = path_1.default.extname(req.file.originalname);
    const fileName = `license_${id}_${Date.now()}${ext}`;
    const filePath = path_1.default.join(__dirname, '../../uploads', fileName);
    fs_1.default.writeFileSync(filePath, req.file.buffer);
    // Store only the path
    const url = `/uploads/${fileName}`;
    const updated = await User_1.default.findByIdAndUpdate(id, { licensePhotoUrl: url }, { new: true });
    res.json({ message: 'License photo uploaded', user: updated });
});
// Upload insurance photo
router.post('/users/:id/insurance', authMiddleware_1.verifyToken, uploadMiddleware_1.default.single('insurance'), async (req, res) => {
    const user = req.user;
    const { id } = req.params;
    if (!user || (user.role !== 'admin' && user.id !== id))
        return res.status(403).json({ message: 'Forbidden' });
    if (!req.file)
        return res.status(400).json({ message: 'No file uploaded' });
    const ext = path_1.default.extname(req.file.originalname);
    const fileName = `insurance_${id}_${Date.now()}${ext}`;
    const filePath = path_1.default.join(__dirname, '../../uploads', fileName);
    fs_1.default.writeFileSync(filePath, req.file.buffer);
    // Store only the path
    const url = `/uploads/${fileName}`;
    const updated = await User_1.default.findByIdAndUpdate(id, { insurancePhotoUrl: url }, { new: true });
    res.json({ message: 'Insurance photo uploaded', user: updated });
});
// Get user by id (self or admin)
router.get('/users/:id', authMiddleware_1.verifyToken, async (req, res) => {
    const user = req.user;
    const { id } = req.params;
    if (!user || (user.role !== 'admin' && user.id !== id))
        return res.status(403).json({ message: 'Forbidden' });
    try {
        const found = await User_1.default.findById(id).select('username email role licensePhotoUrl insurancePhotoUrl licenseExp insuranceExp status');
        if (!found)
            return res.status(404).json({ message: 'User not found' });
        res.json(found);
    }
    catch (err) {
        res.status(500).json({ message: 'Failed to fetch user' });
    }
});
exports.default = router;
//# sourceMappingURL=auth.js.map