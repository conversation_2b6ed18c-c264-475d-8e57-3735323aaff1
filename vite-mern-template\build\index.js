"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const dotenv_1 = __importDefault(require("dotenv"));
const cors_1 = __importDefault(require("cors"));
const db_1 = __importDefault(require("./config/db"));
const auth_1 = __importDefault(require("./routes/auth"));
const PurchaseOrder_1 = __importDefault(require("./routes/PurchaseOrder")); // <-- match filename exactly
const Client_1 = __importDefault(require("./routes/Client"));
const Job_1 = __importDefault(require("./routes/Job"));
const geocode_1 = __importDefault(require("./routes/geocode"));
const dispatch_1 = __importDefault(require("./routes/dispatch"));
const path_1 = __importDefault(require("path"));
dotenv_1.default.config();
console.log('[DEBUG] SENDGRID_API_KEY loaded:', process.env.SENDGRID_API_KEY?.slice(0, 10)); // Should start with 'SG.'
(0, db_1.default)();
const app = (0, express_1.default)();
// Middleware
app.use((0, cors_1.default)({
    origin: true, // Reflects the request origin, allowing any page
    credentials: true
}));
app.use(express_1.default.json());
// Serve uploads directory as static files
app.use('/uploads', express_1.default.static(path_1.default.join(__dirname, '../../uploads')));
// Routes
app.use('/api/auth', auth_1.default);
app.use('/api/purchase-orders', PurchaseOrder_1.default);
app.use('/api/clients', Client_1.default);
app.use('/api/jobs', Job_1.default);
app.use('/api/geocode', geocode_1.default);
app.use('/api/dispatch', dispatch_1.default);
// Error handling middleware
app.use((err, req, res, next) => {
    console.error(err.stack);
    res.status(500).json({ message: 'Something went wrong!' });
});
const PORT = process.env.PORT || 5000;
app.listen(PORT, () => {
    console.log(`Server running on port ${PORT}`);
});
//# sourceMappingURL=index.js.map