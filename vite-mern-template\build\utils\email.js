"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendEmail = sendEmail;
const mail_1 = __importDefault(require("@sendgrid/mail"));
// Hardcode your SendGrid API key here
mail_1.default.setApiKey('*********************************************************************');
async function sendEmail(to, subject, text, html) {
    const msg = {
        to,
        from: '<EMAIL>', // Replace with your verified sender email
        subject,
        text,
        html,
    };
    await mail_1.default.send(msg);
}
//# sourceMappingURL=email.js.map