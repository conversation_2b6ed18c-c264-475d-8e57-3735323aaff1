import mongoose, { Schema } from 'mongoose';

interface IPendingAssignment {
  _id?: mongoose.Types.ObjectId;
  vrpSolutionId: string; // Unique ID for the VRP solution batch
  poId: mongoose.Types.ObjectId;
  truckerId: mongoose.Types.ObjectId;
  deliveryLocation: string;
  materials: {
    materialId: string | number;
    materialName: string;
    quantity: number;
    loadType: string;
  }[];
  totalQuantity: number;
  tripNumber: number;
  estimatedDistance: number;
  estimatedTime: number;
  assignmentSource: 'auto' | 'manual';
  createdAt: Date;
  createdBy: mongoose.Types.ObjectId;
  status: 'pending' | 'approved' | 'rejected' | 'modified';
  rejectionReason?: string;
  originalAssignmentId?: string; // For tracking modifications
}

const PendingAssignmentSchema = new Schema<IPendingAssignment>({
  vrpSolutionId: { type: String, required: true },
  poId: { type: Schema.Types.ObjectId, ref: 'PurchaseOrder', required: true },
  truckerId: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  deliveryLocation: { type: String, required: true },
  materials: [{
    materialId: { type: Schema.Types.Mixed, required: true },
    materialName: { type: String, required: true },
    quantity: { type: Number, required: true },
    loadType: { type: String, required: true }
  }],
  totalQuantity: { type: Number, required: true },
  tripNumber: { type: Number, required: true },
  estimatedDistance: { type: Number, required: true },
  estimatedTime: { type: Number, required: true },
  assignmentSource: { type: String, enum: ['auto', 'manual'], required: true },
  createdAt: { type: Date, default: Date.now },
  createdBy: { type: Schema.Types.ObjectId, ref: 'User', required: true },
  status: { type: String, enum: ['pending', 'approved', 'rejected', 'modified'], default: 'pending' },
  rejectionReason: { type: String },
  originalAssignmentId: { type: String }
});

// Index for efficient queries
PendingAssignmentSchema.index({ vrpSolutionId: 1, status: 1 });
PendingAssignmentSchema.index({ truckerId: 1, status: 1 });
PendingAssignmentSchema.index({ poId: 1, status: 1 });

export const PendingAssignment = mongoose.model<IPendingAssignment>('PendingAssignment', PendingAssignmentSchema);
export type { IPendingAssignment };
