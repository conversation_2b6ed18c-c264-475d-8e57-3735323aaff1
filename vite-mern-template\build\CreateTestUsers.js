"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const path_1 = __importDefault(require("path"));
const dotenv_1 = __importDefault(require("dotenv"));
const mongoose_1 = __importDefault(require("mongoose"));
const User_1 = __importDefault(require("./models/User"));
const argon2_1 = __importDefault(require("argon2"));
dotenv_1.default.config({ path: path_1.default.resolve(__dirname, '../.env') });
async function createTestUsers() {
    try {
        await mongoose_1.default.connect(process.env.MONGO_URI || '');
        const users = [
            {
                username: 'adminUser',
                email: '<EMAIL>', // add unique email
                password: 'adminPass123',
                role: 'admin',
            },
            {
                username: 'truckerUser',
                email: '<EMAIL>', // add unique email
                password: 'truckerPass123',
                role: 'trucker',
            },
        ];
        for (const userData of users) {
            // Hash the password before saving
            const hashedPassword = await argon2_1.default.hash(userData.password);
            // Check if user already exists to avoid duplicates
            const existingUser = await User_1.default.findOne({ username: userData.username });
            if (existingUser) {
                console.log(`User ${userData.username} already exists.`);
                continue;
            }
            const newUser = new User_1.default({
                username: userData.username,
                email: userData.email,
                password: hashedPassword,
                role: userData.role,
            });
            await newUser.save();
            console.log(`Created user: ${userData.username} with role ${userData.role}`);
        }
        await mongoose_1.default.disconnect();
    }
    catch (error) {
        console.error('Error creating test users:', error);
        process.exit(1);
    }
}
createTestUsers();
//# sourceMappingURL=createTestUsers.js.map