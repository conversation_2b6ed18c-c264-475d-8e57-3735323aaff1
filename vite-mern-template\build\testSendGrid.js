"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const mail_1 = __importDefault(require("@sendgrid/mail"));
// ✅ Hardcode your full SendGrid API key here temporarily
mail_1.default.setApiKey('*********************************************************************');
async function sendTestEmail() {
    try {
        await mail_1.default.send({
            to: '<EMAIL>', // ✅ your own inbox
            from: '<EMAIL>', // ✅ verified in SendGrid
            subject: 'Test from standalone TypeScript',
            text: 'This is a test email sent using a hardcoded SendGrid key.',
        });
        console.log('✅ Email sent successfully!');
    }
    catch (error) {
        console.error('❌ SendGrid error:', error.response?.body || error.message);
    }
}
sendTestEmail();
//# sourceMappingURL=testSendgrid.js.map