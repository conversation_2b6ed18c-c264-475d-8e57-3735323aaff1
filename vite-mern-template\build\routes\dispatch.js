"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const mongoose_1 = __importDefault(require("mongoose"));
const authMiddleware_1 = require("../middleware/authMiddleware");
const vrpService_1 = __importDefault(require("../services/vrpService"));
const User_1 = __importDefault(require("../models/User"));
const PurchaseOrder_1 = require("../models/PurchaseOrder");
const PendingAssignment_1 = require("../models/PendingAssignment");
const router = express_1.default.Router();
// GET /api/dispatch/drivers - Get available drivers with their current status
router.get('/drivers', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can access driver information' });
        }
        const drivers = await vrpService_1.default.getAvailableDrivers();
        res.json(drivers);
    }
    catch (error) {
        console.error('Error fetching drivers:', error);
        res.status(500).json({ error: 'Failed to fetch drivers' });
    }
});
// GET /api/dispatch/unassigned - Get unassigned deliveries
router.get('/unassigned', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can access dispatch information' });
        }
        const unassignedDeliveries = await vrpService_1.default.getUnassignedDeliveries();
        res.json(unassignedDeliveries);
    }
    catch (error) {
        console.error('Error fetching unassigned deliveries:', error);
        res.status(500).json({ error: 'Failed to fetch unassigned deliveries' });
    }
});
// GET /api/dispatch/queue - Get delivery queue with capacity-maximized transactions
router.get('/queue', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can view delivery queue' });
        }
        console.log('📋 Generating delivery queue...');
        const deliveryQueue = await vrpService_1.default.generateDeliveryQueue();
        console.log('✅ Delivery queue generated:', {
            totalTransactions: deliveryQueue.length,
            driversInvolved: new Set(deliveryQueue.map(t => t.driverId)).size
        });
        res.json({ deliveryQueue });
    }
    catch (error) {
        console.error('Error generating delivery queue:', error);
        res.status(500).json({
            error: 'Failed to generate delivery queue',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// POST /api/dispatch/optimize - Generate optimized dispatch solution (preview)
router.post('/optimize', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can optimize dispatch' });
        }
        console.log('🚀 Starting VRP optimization...');
        // Get optimization options from request body
        const { ignoreCapacity = true, // Default to ignoring capacity constraints
        ignoreMaxDeliveries = true, // Default to ignoring max delivery constraints
        capacityMultiplier = 2, // Allow 2x capacity by default
        maxDeliveriesOverride = 20 // Allow up to 20 deliveries by default
         } = req.body;
        const solution = await vrpService_1.default.solveVRP({
            ignoreCapacity,
            ignoreMaxDeliveries,
            capacityMultiplier,
            maxDeliveriesOverride
        });
        console.log('✅ VRP optimization completed:', {
            driversUsed: solution.solutions.length,
            ordersProcessed: solution.optimizationStats.ordersProcessed,
            efficiency: solution.optimizationStats.efficiency,
            deliveryTransactions: solution.deliveryQueue.length
        });
        res.json(solution);
    }
    catch (error) {
        console.error('Error optimizing dispatch:', error);
        res.status(500).json({
            error: 'Failed to optimize dispatch',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// POST /api/dispatch/apply - Apply the optimized solution
router.post('/apply', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can apply dispatch solutions' });
        }
        const { solution } = req.body;
        if (!solution) {
            return res.status(400).json({ message: 'Solution data is required' });
        }
        console.log('📋 Applying VRP solution...');
        await vrpService_1.default.applySolution(solution);
        console.log('✅ VRP solution applied successfully');
        res.json({
            message: 'Dispatch solution applied successfully',
            assignmentsCreated: solution.solutions.reduce((sum, sol) => sum + sol.assignments.length, 0)
        });
    }
    catch (error) {
        console.error('Error applying dispatch solution:', error);
        res.status(500).json({
            error: 'Failed to apply dispatch solution',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// POST /api/dispatch/auto - One-click automatic dispatch (optimize + apply)
router.post('/auto', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can trigger auto-dispatch' });
        }
        console.log('🤖 Starting automatic dispatch...');
        // Step 1: Optimize with flexible constraints
        const solution = await vrpService_1.default.solveVRP({
            ignoreCapacity: true, // Ignore capacity constraints for auto-dispatch
            ignoreMaxDeliveries: true, // Ignore max delivery constraints
            capacityMultiplier: 3, // Allow 3x normal capacity
            maxDeliveriesOverride: 50 // Allow many deliveries per driver
        });
        if (!solution.deliveryQueue || solution.deliveryQueue.length === 0) {
            return res.json({
                message: 'No delivery transactions could be generated',
                reason: 'No available drivers or unassigned orders',
                solution
            });
        }
        // Step 2: Apply
        await vrpService_1.default.applySolution(solution);
        console.log('✅ Automatic dispatch completed successfully');
        res.json({
            message: 'Automatic dispatch completed successfully',
            solution,
            transactionsCreated: solution.deliveryQueue.length,
            driversUsed: new Set(solution.deliveryQueue.map(t => t.driverId)).size,
            ordersProcessed: solution.optimizationStats.ordersProcessed,
            unassignedOrders: solution.unassignedOrders.length
        });
    }
    catch (error) {
        console.error('Error in automatic dispatch:', error);
        res.status(500).json({
            error: 'Failed to complete automatic dispatch',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// POST /api/dispatch/reset - Reset all current assignments (remove all non-completed transactions)
router.post('/reset', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can reset assignments' });
        }
        // Find all POs with non-completed transactions
        const purchaseOrders = await PurchaseOrder_1.PurchaseOrder.find({
            'transactions.status': { $ne: 'Completed' }
        });
        let totalAssignmentsRemoved = 0;
        for (const po of purchaseOrders) {
            const originalCount = po.transactions.length;
            // Keep only completed transactions
            po.transactions = po.transactions.filter((t) => t.status === 'Completed');
            totalAssignmentsRemoved += (originalCount - po.transactions.length);
            await po.save();
        }
        res.json({ message: 'All current assignments have been reset', assignmentsRemoved: totalAssignmentsRemoved });
    }
    catch (error) {
        console.error('Error resetting assignments:', error);
        res.status(500).json({ error: 'Failed to reset assignments', details: error instanceof Error ? error.message : 'Unknown error' });
    }
});
// POST /api/dispatch/reassign-driver - Remove all assignments for a driver and redistribute, keep only the next reserved for other drivers after their last picked up/completed
router.post('/reassign-driver', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can reassign drivers' });
        }
        const { driverId } = req.body;
        if (!driverId) {
            return res.status(400).json({ message: 'driverId is required' });
        }
        // Remove all assignments for the deactivated driver
        const purchaseOrders = await PurchaseOrder_1.PurchaseOrder.find({});
        let assignmentsRemoved = 0;
        for (const po of purchaseOrders) {
            const before = po.transactions.length;
            po.transactions = po.transactions.filter(t => !t.truckerId.equals(driverId));
            assignmentsRemoved += before - po.transactions.length;
            await po.save();
        }
        // For all other drivers, keep only their next reserved after their last picked up/completed
        // 1. Get all active driver IDs (excluding deactivated)
        const User = require('../models/User').default || require('../models/User');
        const activeDrivers = await User.find({ role: 'trucker', status: 'Active', _id: { $ne: driverId } });
        for (const driver of activeDrivers) {
            const driverIdStr = driver._id.toString();
            // Gather all transactions for this driver across all POs
            let allTxs = [];
            for (const po of purchaseOrders) {
                for (const t of po.transactions) {
                    if (t.truckerId && t.truckerId.toString() === driverIdStr) {
                        // No .toObject() needed, just use t and keep reference to po
                        allTxs.push({ ...t, po });
                    }
                }
            }
            // Sort all transactions by reservedAt (fallback to 0 if missing)
            allTxs = allTxs.sort((a, b) => {
                const aDate = a.reservedAt ? new Date(a.reservedAt).getTime() : 0;
                const bDate = b.reservedAt ? new Date(b.reservedAt).getTime() : 0;
                return aDate - bDate;
            });
            // Find the last PickedUp or Completed
            let lastActiveIdx = -1;
            for (let i = 0; i < allTxs.length; i++) {
                const tx = allTxs[i];
                if (tx && (tx.status === 'PickedUp' || tx.status === 'Completed')) {
                    lastActiveIdx = i;
                }
            }
            // Find the first Reserved after lastActiveIdx
            let nextReservedId = null;
            for (let i = lastActiveIdx + 1; i < allTxs.length; i++) {
                const tx = allTxs[i];
                if (tx && tx.status === 'Reserved') {
                    const txId = tx._id;
                    if (txId && typeof txId.toString === 'function') {
                        nextReservedId = txId.toString();
                    }
                    break;
                }
            }
            // Remove all other Reserved for this driver
            for (const po of purchaseOrders) {
                const before = po.transactions.length;
                po.transactions = po.transactions.filter(t => {
                    if (t && t.truckerId && t.status === 'Reserved') {
                        if (t.truckerId.toString() === driverIdStr) {
                            // Only keep the nextReservedId (if any)
                            return t._id && nextReservedId && typeof t._id.toString === 'function' && t._id.toString() === nextReservedId;
                        }
                    }
                    return true;
                });
                assignmentsRemoved += before - po.transactions.length;
                await po.save();
            }
        }
        // Re-run auto-dispatch to redistribute unassigned deliveries (excluding the deactivated driver)
        const solution = await vrpService_1.default.solveVRP({
            ignoreCapacity: true,
            ignoreMaxDeliveries: true,
            capacityMultiplier: 3,
            maxDeliveriesOverride: 50
        });
        await vrpService_1.default.applySolution(solution);
        res.json({ message: 'Assignments for driver removed and deliveries reassigned', assignmentsRemoved, solution });
    }
    catch (error) {
        console.error('Error reassigning driver:', error);
        res.status(500).json({ error: 'Failed to reassign driver', details: error instanceof Error ? error.message : 'Unknown error' });
    }
});
// PUT /api/dispatch/driver/:driverId/location - Update driver location
router.put('/driver/:driverId/location', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        const { driverId } = req.params;
        const { latitude, longitude, address } = req.body;
        // Allow drivers to update their own location or admins to update any driver
        if (user && user.role !== 'admin' && user.id !== driverId) {
            return res.status(403).json({ message: 'You can only update your own location' });
        }
        const driver = await User_1.default.findById(driverId);
        if (!driver || driver.role !== 'trucker') {
            return res.status(404).json({ message: 'Driver not found' });
        }
        driver.currentLocation = {
            latitude,
            longitude,
            address,
            updatedAt: new Date()
        };
        await driver.save();
        res.json({
            message: 'Driver location updated successfully',
            location: driver.currentLocation
        });
    }
    catch (error) {
        console.error('Error updating driver location:', error);
        res.status(500).json({ error: 'Failed to update driver location' });
    }
});
// PUT /api/dispatch/driver/:driverId/availability - Update driver availability
router.put('/driver/:driverId/availability', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        const { driverId } = req.params;
        const { isAvailable } = req.body;
        // Allow drivers to update their own availability or admins to update any driver
        if (user && user.role !== 'admin' && user.id !== driverId) {
            return res.status(403).json({ message: 'You can only update your own availability' });
        }
        const driver = await User_1.default.findById(driverId);
        if (!driver || driver.role !== 'trucker') {
            return res.status(404).json({ message: 'Driver not found' });
        }
        driver.isAvailable = isAvailable;
        await driver.save();
        res.json({
            message: 'Driver availability updated successfully',
            isAvailable: driver.isAvailable
        });
    }
    catch (error) {
        console.error('Error updating driver availability:', error);
        res.status(500).json({ error: 'Failed to update driver availability' });
    }
});
// GET /api/dispatch/stats - Get dispatch statistics
router.get('/stats', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can access dispatch statistics' });
        }
        // Driver statistics
        const totalDrivers = await User_1.default.countDocuments({ role: 'trucker', status: 'Active' });
        const availableDrivers = await User_1.default.countDocuments({
            role: 'trucker',
            status: 'Active',
            isAvailable: { $ne: false }
        });
        const busyDrivers = totalDrivers - availableDrivers;
        // Purchase Order statistics
        const totalPOs = await PurchaseOrder_1.PurchaseOrder.countDocuments();
        const openPOs = await PurchaseOrder_1.PurchaseOrder.countDocuments({ isCompleted: false });
        const completedPOs = await PurchaseOrder_1.PurchaseOrder.countDocuments({ isCompleted: true });
        // PO assignment statistics
        const assignedPOs = await PurchaseOrder_1.PurchaseOrder.countDocuments({
            isCompleted: false,
            'transactions.0': { $exists: true }
        });
        const unassignedPOs = openPOs - assignedPOs;
        // Trip statistics (from transactions)
        const allPOs = await PurchaseOrder_1.PurchaseOrder.find();
        let totalTripsDispatched = 0;
        let tripsInProgress = 0;
        let tripsCompleted = 0;
        for (const po of allPOs) {
            if (po.transactions && po.transactions.length > 0) {
                totalTripsDispatched += po.transactions.length;
                for (const transaction of po.transactions) {
                    if (transaction.status === 'Completed') {
                        tripsCompleted++;
                    }
                    else if (transaction.status === 'Reserved' || transaction.status === 'PickedUp') {
                        tripsInProgress++;
                    }
                }
            }
        }
        // Calculate efficiency metrics
        const poCompletionRate = totalPOs > 0 ? (completedPOs / totalPOs) * 100 : 100;
        const tripCompletionRate = totalTripsDispatched > 0 ? (tripsCompleted / totalTripsDispatched) * 100 : 100;
        const driverUtilization = totalDrivers > 0 ? ((totalDrivers - availableDrivers) / totalDrivers) * 100 : 0;
        // Daily capacity estimate
        const avgTripsPerDriver = totalDrivers > 0 ? totalTripsDispatched / totalDrivers : 0;
        const estimatedDailyCapacity = availableDrivers * 50; // Conservative estimate based on auto-dispatch settings
        res.json({
            drivers: {
                total: totalDrivers,
                available: availableDrivers,
                busy: busyDrivers,
                utilization: Math.round(driverUtilization * 10) / 10
            },
            purchaseOrders: {
                total: totalPOs,
                open: openPOs,
                completed: completedPOs,
                assigned: assignedPOs,
                unassigned: unassignedPOs,
                completionRate: Math.round(poCompletionRate * 10) / 10
            },
            trips: {
                totalDispatched: totalTripsDispatched,
                inProgress: tripsInProgress,
                completed: tripsCompleted,
                completionRate: Math.round(tripCompletionRate * 10) / 10,
                avgPerDriver: Math.round(avgTripsPerDriver * 10) / 10
            },
            capacity: {
                estimatedDailyCapacity,
                currentUtilization: totalTripsDispatched,
                availableDrivers
            }
        });
    }
    catch (error) {
        console.error('Error fetching dispatch stats:', error);
        res.status(500).json({ error: 'Failed to fetch dispatch statistics' });
    }
});
// POST /api/dispatch/optimize-pending - Create pending assignments (manual approval required)
router.post('/optimize-pending', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can create pending assignments' });
        }
        console.log('🔄 Creating pending assignments...');
        const solution = await vrpService_1.default.solveVRP({
            ignoreCapacity: true,
            ignoreMaxDeliveries: true,
            capacityMultiplier: 3,
            maxDeliveriesOverride: 50
        });
        if (!solution.deliveryQueue || solution.deliveryQueue.length === 0) {
            return res.json({
                message: 'No delivery transactions could be generated',
                reason: 'No available drivers or unassigned orders',
                solution
            });
        }
        // Create pending assignments instead of applying directly
        const vrpSolutionId = `vrp_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        const pendingAssignments = [];
        for (const transaction of solution.deliveryQueue) {
            const pendingAssignment = new PendingAssignment_1.PendingAssignment({
                vrpSolutionId,
                poId: transaction.poId,
                truckerId: transaction.driverId,
                deliveryLocation: transaction.deliveryLocation,
                materials: transaction.materials,
                totalQuantity: transaction.totalQuantity,
                tripNumber: transaction.tripNumber,
                estimatedDistance: transaction.estimatedDistance,
                estimatedTime: transaction.estimatedTime,
                assignmentSource: 'auto',
                createdBy: user.id,
                status: 'pending'
            });
            await pendingAssignment.save();
            pendingAssignments.push(pendingAssignment);
        }
        console.log(`✅ Created ${pendingAssignments.length} pending assignments`);
        res.json({
            message: 'Pending assignments created successfully',
            vrpSolutionId,
            pendingAssignments: pendingAssignments.length,
            solution
        });
    }
    catch (error) {
        console.error('Error creating pending assignments:', error);
        res.status(500).json({
            error: 'Failed to create pending assignments',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
// GET /api/dispatch/pending - Get all pending assignments
router.get('/pending', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can view pending assignments' });
        }
        const pendingAssignments = await PendingAssignment_1.PendingAssignment.find({ status: 'pending' })
            .populate('truckerId', 'username')
            .populate('poId', 'jobName')
            .sort({ createdAt: -1 });
        res.json({ pendingAssignments });
    }
    catch (error) {
        console.error('Error fetching pending assignments:', error);
        res.status(500).json({ error: 'Failed to fetch pending assignments' });
    }
});
// POST /api/dispatch/approve - Approve pending assignments
router.post('/approve', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can approve assignments' });
        }
        const { assignmentIds, vrpSolutionId } = req.body;
        if (!assignmentIds || !Array.isArray(assignmentIds)) {
            return res.status(400).json({ message: 'Assignment IDs array is required' });
        }
        // Update pending assignments to approved
        const approvedAssignments = await PendingAssignment_1.PendingAssignment.find({
            _id: { $in: assignmentIds },
            status: 'pending'
        });
        if (approvedAssignments.length === 0) {
            return res.status(404).json({ message: 'No pending assignments found' });
        }
        // Apply approved assignments to purchase orders
        for (const assignment of approvedAssignments) {
            const po = await PurchaseOrder_1.PurchaseOrder.findById(assignment.poId);
            if (po) {
                po.transactions.push({
                    truckerId: assignment.truckerId,
                    status: 'Reserved',
                    reservedAt: new Date(),
                    reservedMaterials: assignment.materials.map((mat) => ({
                        materialId: mat.materialId,
                        materialName: mat.materialName,
                        quantity: mat.quantity,
                        deliveryLocationName: assignment.deliveryLocation
                    })),
                    assignmentSource: assignment.assignmentSource,
                    isApproved: true,
                    approvedBy: new mongoose_1.default.Types.ObjectId(user.id),
                    approvedAt: new Date(),
                    lastModified: new Date(),
                    originalAssignmentId: assignment._id?.toString()
                });
                await po.save();
            }
            // Update assignment status
            assignment.status = 'approved';
            await assignment.save();
        }
        res.json({
            message: 'Assignments approved successfully',
            approvedCount: approvedAssignments.length
        });
    }
    catch (error) {
        console.error('Error approving assignments:', error);
        res.status(500).json({ error: 'Failed to approve assignments' });
    }
});
// POST /api/dispatch/reject - Reject pending assignments
router.post('/reject', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can reject assignments' });
        }
        const { assignmentIds, reason } = req.body;
        if (!assignmentIds || !Array.isArray(assignmentIds)) {
            return res.status(400).json({ message: 'Assignment IDs array is required' });
        }
        const rejectedCount = await PendingAssignment_1.PendingAssignment.updateMany({ _id: { $in: assignmentIds }, status: 'pending' }, {
            status: 'rejected',
            rejectionReason: reason || 'No reason provided'
        });
        res.json({
            message: 'Assignments rejected successfully',
            rejectedCount: rejectedCount.modifiedCount
        });
    }
    catch (error) {
        console.error('Error rejecting assignments:', error);
        res.status(500).json({ error: 'Failed to reject assignments' });
    }
});
// PUT /api/dispatch/assignment/:id - Edit a pending assignment
router.put('/assignment/:id', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can edit assignments' });
        }
        const { id } = req.params;
        const { truckerId, materials, deliveryLocation } = req.body;
        const assignment = await PendingAssignment_1.PendingAssignment.findById(id);
        if (!assignment || assignment.status !== 'pending') {
            return res.status(404).json({ message: 'Pending assignment not found' });
        }
        // Update assignment fields
        if (truckerId)
            assignment.truckerId = new mongoose_1.default.Types.ObjectId(truckerId);
        if (materials)
            assignment.materials = materials;
        if (deliveryLocation)
            assignment.deliveryLocation = deliveryLocation;
        assignment.assignmentSource = 'manual';
        assignment.status = 'modified';
        await assignment.save();
        res.json({
            message: 'Assignment updated successfully',
            assignment
        });
    }
    catch (error) {
        console.error('Error updating assignment:', error);
        res.status(500).json({ error: 'Failed to update assignment' });
    }
});
// POST /api/dispatch/partial-rerun - Partial rerun for specific drivers/orders
router.post('/partial-rerun', authMiddleware_1.verifyToken, async (req, res) => {
    try {
        const user = req.user;
        if (!user || user.role !== 'admin') {
            return res.status(403).json({ message: 'Only admins can trigger partial reruns' });
        }
        const { affectedDriverIds, affectedPoIds } = req.body;
        console.log('🔄 Starting partial rerun...');
        // Remove existing assignments for affected drivers/POs
        if (affectedDriverIds && affectedDriverIds.length > 0) {
            const purchaseOrders = await PurchaseOrder_1.PurchaseOrder.find({
                'transactions.truckerId': { $in: affectedDriverIds.map((id) => new mongoose_1.default.Types.ObjectId(id)) },
                'transactions.status': { $ne: 'Completed' }
            });
            for (const po of purchaseOrders) {
                po.transactions = po.transactions.filter(t => !affectedDriverIds.includes(t.truckerId.toString()) || t.status === 'Completed');
                await po.save();
            }
        }
        if (affectedPoIds && affectedPoIds.length > 0) {
            const purchaseOrders = await PurchaseOrder_1.PurchaseOrder.find({
                _id: { $in: affectedPoIds.map((id) => new mongoose_1.default.Types.ObjectId(id)) }
            });
            for (const po of purchaseOrders) {
                po.transactions = po.transactions.filter(t => t.status === 'Completed');
                await po.save();
            }
        }
        // Run VRP optimization for the affected subset
        const solution = await vrpService_1.default.solveVRP({
            ignoreCapacity: true,
            ignoreMaxDeliveries: true,
            capacityMultiplier: 3,
            maxDeliveriesOverride: 50
        });
        // Apply the new solution
        if (solution.deliveryQueue && solution.deliveryQueue.length > 0) {
            await vrpService_1.default.applySolution(solution);
        }
        console.log('✅ Partial rerun completed successfully');
        res.json({
            message: 'Partial rerun completed successfully',
            solution,
            transactionsCreated: solution.deliveryQueue?.length || 0,
            affectedDrivers: affectedDriverIds?.length || 0,
            affectedPOs: affectedPoIds?.length || 0
        });
    }
    catch (error) {
        console.error('Error in partial rerun:', error);
        res.status(500).json({
            error: 'Failed to complete partial rerun',
            details: error instanceof Error ? error.message : 'Unknown error'
        });
    }
});
exports.default = router;
//# sourceMappingURL=dispatch.js.map